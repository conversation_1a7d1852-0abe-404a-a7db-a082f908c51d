import { AgGridReact } from "ag-grid-react";
import { useState } from "react";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-quartz.css";
import Button from "@/components/Buttons/Button";
import { DisplayItem } from "@/components/DisplayItem";
import { DisplayNPCImage } from "@/components/DisplayNPCImage";
import useBeginRooftopBattle from "@/features/battle/api/useBeginRooftopBattle";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import useGameConfig from "@/hooks/useGameConfig";
import { cn } from "@/lib/utils";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";

const DisplayRewardCell = (props) => {
    const { value } = props;
    if (!value) return <p className="mt-6 text-bold text-gray-400 text-lg">?</p>;

    return (
        <div className="relative flex size-full p-5 md:p-3">
            <DisplayItem item={value} className="mx-auto" height="h-full" />
            {props.data?.itemRewardQuantity > 1 && (
                <p className="-translate-x-1/2 absolute bottom-4 left-1/2 rounded-lg bg-black/25 px-1 text-center font-bold text-custom-yellow text-sm leading-0 md:bottom-1.5">
                    x{props.data?.itemRewardQuantity}
                </p>
            )}
        </div>
    );
};

const DisplayNPCCell = (props) => {
    const { value } = props;

    return (
        <div
            className={cn(
                props.data?.defeated && "grayscale",
                props.data?.disabled && "opacity-25 grayscale",
                "relative flex h-full flex-col items-center justify-center py-0.5 md:w-full md:flex-row md:items-start md:justify-normal md:gap-4 md:p-2"
            )}
        >
            <DisplayNPCImage
                src={props.data.image}
                className="size-14 rounded-lg border border-blue-800 md:h-full md:w-auto"
            />

            <p className="text-wrap! text-center! text-xs! 2xl:text-base! font-semibold text-blue-400 md:my-auto">
                {value}
            </p>
            <p className="text-custom-yellow text-xs md:hidden">{props.data.rank} Rank</p>
        </div>
    );
};

const BattleList = ({ npcList, currentUser }) => {
    const navigate = useNavigate();
    const isMobile = useCheckMobileScreen();
    const { ROOFTOP_BATTLE_AP_COST } = useGameConfig();
    const beginRooftopBattleMutation = useBeginRooftopBattle();

    const handleRooftopBeginBattle = async (id) => {
        if (currentUser?.actionPoints < ROOFTOP_BATTLE_AP_COST) return toast.error("Not enough AP");

        try {
            await beginRooftopBattleMutation.mutateAsync({
                battleOpponentId: parseInt(id),
            });
            navigate("/fight");
        } catch (error) {
            const errorMessage = error.message || "Unknown error occurred";
            console.error(errorMessage);
            toast.error(errorMessage);
        }
    };

    const FightNPCCell = (props) => {
        return (
            <div className="flex size-full items-center justify-center">
                <Button
                    disabled={props.data?.defeated || props.data?.disabled}
                    className="text-xs! md:text-sm! w-[90%] 2xl:w-1/2"
                    type="danger"
                    onClick={() => handleRooftopBeginBattle(props.data.id)}
                >
                    {props.data?.defeated ? (
                        "Defeated"
                    ) : (
                        <div className="flex flex-col">
                            <p className="md:text-sm!">Attack</p>
                            <p className="text-xs! md:-mt-1 mb-1">{ROOFTOP_BATTLE_AP_COST} AP</p>
                        </div>
                    )}
                </Button>
            </div>
        );
    };

    const [colDefs, setColDefs] = useState([
        {
            headerName: "NPC",
            field: "name",
            cellRenderer: DisplayNPCCell,
        },
        {
            headerName: "Rank",
            field: "rank",
            hide: isMobile,
            cellClass: "mt-4 text-xl font-semibold text-custom-yellow",
        },
        {
            // headerName: "Combat Power",
            headerName: "Level",
            field: "level",
            wrapHeaderText: true,
            autoHeaderHeight: true,
            cellClass: "mt-5 text-lg font-bold text-red-500 !md:px-0",
            maxWidth: isMobile ? 67 : null,
            // headerClass: "text-[0.6rem]! md:text-base!",
        },
        {
            headerName: "Reward",
            field: "item",
            cellRenderer: DisplayRewardCell,
            sortable: false,
        },

        {
            headerName: "",
            field: "static",
            cellRenderer: FightNPCCell,
            sortable: false,
        },
    ]);

    const defaultColDef = {
        flex: 1,
        sortable: true,
        suppressMovable: true,
        filter: false,
        resizable: false,
        cellClass: "px-0.5! md:px-2! 2xl:px-6!",
    };

    const autoSizeStrategy = {
        type: "fitCellContents",
    };

    return (
        <div className="ag-theme-quartz-dark" style={{ width: "100%", overflow: "auto" }}>
            <AgGridReact
                suppressCellFocus
                suppressRowHoverHighlight
                rowData={npcList}
                columnDefs={colDefs}
                defaultColDef={defaultColDef}
                domLayout={"autoHeight"}
                rowHeight={isMobile ? 100 : 80}
            />
        </div>
    );
};

export default BattleList;
