import { orpc, adminOrpc } from "@/lib/orpc";

export const api = {
    // Authentication Routes (better-auth)
    auth: {
        login: "/auth/login",
        reqPasswordReset: "/auth/requestPasswordReset",
        passwordReset: "/auth/resetPassword",
        logout: "/auth/logout",
        // Registration Routes (ORPC)
        register: orpc.auth.register,
        checkUsername: orpc.auth.checkUsername,
    },

    // Admin Routes
    admin: {
        chatBanUser: adminOrpc.chatBanUser,
        hideSingleMessage: adminOrpc.hideSingleMessage,
        unhideSingleMessage: adminOrpc.unhideSingleMessage,
        deleteSingleMessage: adminOrpc.deleteSingleMessage,
        getGangInfo: adminOrpc.getGangInfo,
        createTestUser: adminOrpc.createTestUser,
    },

    // User Routes
    user: {
        getUserList: orpc.user.getUserList,
        getCurrentUserInfo: orpc.user.getCurrentUserInfo,
        getUserInfo: orpc.user.getUserInfo,
        getInventory: orpc.user.getInventory,
        getEquippedItems: orpc.user.getEquippedItems,
        getTradeableInventory: orpc.user.getTradeableInventory,
        getStatusEffects: orpc.user.getStatusEffects,
        updateProfileDetails: orpc.user.updateProfileDetails,
        train: orpc.user.train,
        equipItem: orpc.user.equipItem,
        unequipItem: orpc.user.unequipItem,
        useItem: orpc.user.useItem,
        getGameConfig: orpc.user.getGameConfig,
        linkDiscord: orpc.user.linkDiscord,
        setLastNewsIDRead: orpc.user.setLastNewsIDRead,
        getSkills: orpc.user.getSkills,
    },

    // Profile Routes
    profile: {
        getComments: orpc.profileComment.getComments,
        postComment: orpc.profileComment.postComment,
    },

    // Registration Codes Routes (ORPC)
    registrationCodes: {
        getReferralCodes: orpc.auth.getReferralCodes,
        checkCode: orpc.auth.checkCode,
    },

    // Gang Routes
    gang: {
        getGangList: orpc.gang.getGangList,
        getGangInfo: orpc.gang.getGangInfo,
        createGang: orpc.gang.createGang,
        getCurrentGang: orpc.gang.getCurrentGang,
        getMemberShares: orpc.gang.getMemberShares,
        getGangLogs: orpc.gang.getGangLogs,
        getInviteList: orpc.gang.getInviteList,
        hasGangSigil: orpc.gang.hasGangSigil,
        inviteMember: orpc.gang.inviteMember,
        getCurrentInvites: orpc.gang.getCurrentInvites,
        acceptInvite: orpc.gang.acceptInvite,
        declineInvite: orpc.gang.declineInvite,
        assignRank: orpc.gang.assignRank,
        updatePayoutShares: orpc.gang.updatePayoutShares,
        upgradeHideout: orpc.gang.upgradeHideout,
        leaveGang: orpc.gang.leaveGang,
        requestInvite: orpc.gang.requestInvite,
        updateGangInfo: orpc.gang.updateGangInfo,
        kickMember: orpc.gang.kickMember,
    },

    // Items Routes
    items: {
        getUpgradeItems: orpc.item.getUpgradeItems,
        upgradeItem: orpc.item.upgradeItem,
        getItemList: adminOrpc.item.list, // Dev only route
    },

    // Bank Routes
    bank: {
        withdraw: orpc.bank.withdraw,
        deposit: orpc.bank.deposit,
        transfer: orpc.bank.transfer,
        getBankTransactions: orpc.bank.getBankTransactions,
    },

    // Shops Routes
    shop: {
        shopList: orpc.shop.shopList,
        shopInfo: orpc.shop.shopInfo,
        purchaseItem: orpc.shop.purchaseItem,
        sellItem: orpc.shop.sellItem,
        getTraderRep: orpc.shop.getTraderRep,
    },

    // Job Routes
    jobs: {
        list: orpc.job.list,
        info: orpc.job.info,
        apply: orpc.job.apply,
        promote: orpc.job.promote,
        getRequirements: orpc.job.getRequirements,
        changePayoutTime: orpc.job.changePayoutTime,
    },

    battle: {
        begin: orpc.battle.begin,
        attack: orpc.battle.attack,
        postBattleAction: orpc.battle.postBattleAction,
        getStatus: orpc.battle.getStatus,
    },

    // Chat Routes
    chat: {
        getHistory: orpc.chat.getHistory,
        getRooms: orpc.chat.getRooms,
    },

    // Infirmary Routes
    infirmary: {
        getHospitalList: orpc.infirmary.getHospitalList,
        getInjuredList: orpc.infirmary.getInjuredList,
        revivePlayer: orpc.infirmary.revivePlayer,
        hospitalCheckIn: orpc.infirmary.hospitalCheckIn,
    },

    // Jail Routes
    jail: {
        jailList: orpc.jail.jailList,
        bail: orpc.jail.bail,
    },

    // Property Routes
    property: {
        getHousingList: orpc.property.getHousingList,
        getUserProperties: orpc.property.getUserProperties,
        purchaseProperty: orpc.property.purchaseProperty,
        sellProperty: orpc.property.sellProperty,
        setPrimaryProperty: orpc.property.setPrimaryProperty,
    },

    // Crafting Routes
    crafting: {
        getCraftingQueue: orpc.crafting.getCraftingQueue,
        getRecipes: orpc.crafting.getRecipes,
        craftItem: orpc.crafting.craftItem,
        completeCraft: orpc.crafting.completeCraft,
        cancelCraft: orpc.crafting.cancelCraft,
    },

    // Private Messaging Routes
    messaging: {
        getChatHistory: orpc.privateMessage.getChatHistory,
        getUnreadCount: orpc.privateMessage.getUnreadCount,
        sendMessage: orpc.privateMessage.sendMessage,
        markMessageRead: orpc.privateMessage.markMessageRead,
    },

    // Roguelike Routes
    roguelike: {
        getCurrentMap: orpc.roguelike.getCurrentMap,
        beginRun: orpc.roguelike.beginRun,
        advance: orpc.roguelike.advance,
        activateNode: orpc.roguelike.activateNode,
        chooseScavengeOption: orpc.roguelike.chooseScavengeOption,
    },

    // Notifications Routes
    notifications: {
        getList: orpc.notification.getList,
        getUnreadCount: orpc.notification.getUnreadCount,
        markRead: orpc.notification.markRead,
        updatePushSettings: orpc.notification.updatePushSettings,
        saveFCMToken: orpc.notification.saveFCMToken,
    },

    // Leaderboards Routes
    leaderboards: {
        getLeaderBoards: orpc.leaderboard.getLeaderBoards,
        getChatEmoteLeaderboards: orpc.leaderboard.getChatEmoteLeaderboards,
    },

    // Courses/Dojo Routes
    courses: {
        list: orpc.course.list,
        start: orpc.course.start,
    },

    // Casino Routes
    casino: {
        gamble: orpc.casino.gamble,
        getLottery: orpc.casino.getLottery,
        enterLottery: orpc.casino.enterLottery,
        checkLotteryEntry: orpc.casino.checkLotteryEntry,
        placeBet: orpc.casino.placeBet,
    },

    // Special/Unique Items Routes
    specialItems: {
        useDeathNote: orpc.item.useDeathNote,
        useLifeNote: orpc.item.useLifeNote,
        useKompromat: orpc.item.useKompromat,
        useMegaphone: orpc.item.useMegaphone,
        useDailyChest: orpc.item.useDailyChest,
        getDailyChestItems: orpc.item.getDailyChestItems,
        useMaterialsCrate: orpc.item.useMaterialsCrate,
        useToolsCrate: orpc.item.useToolsCrate,
    },

    // Pets Routes
    pets: {
        list: orpc.pets.list,
        feed: orpc.pets.feed,
        play: orpc.pets.play,
        train: orpc.pets.train,
        customize: orpc.pets.customize,
        evolve: orpc.pets.evolve,
        setActive: orpc.pets.setActive,
    },

    // Talents Routes
    talents: {
        getTalents: orpc.talents.getTalents,
        getUnlockedTalents: orpc.talents.getUnlockedTalents,
        getEquippedAbilities: orpc.talents.getEquippedAbilities,
        unlockTalent: orpc.talents.unlockTalent,
        equipAbility: orpc.talents.equipAbility,
        unequipAbility: orpc.talents.unequipAbility,
        resetTalents: orpc.talents.resetTalents,
    },

    // Quests/Tasks Routes
    quests: {
        getProgress: orpc.quest.getProgress,
        getAvailable: orpc.quest.getAvailable,
        start: orpc.quest.start,
        complete: orpc.quest.complete,
        getCombinedList: orpc.quest.getCombinedList,
        getActive: orpc.quest.getActive,
        getCompleted: orpc.quest.getCompleted,
        handInItem: orpc.quest.handInItem,
        getStoryQuests: orpc.quest.getStoryQuests,
    },

    // Daily Quest Routes
    dailyQuest: {
        getDailyQuests: orpc.dailyQuest.getDailyQuests,
        completeDailyQuest: orpc.dailyQuest.completeDailyQuest,
        claimDailyCompletionReward: orpc.dailyQuest.claimDailyCompletionReward,
    },

    // Bounty Routes
    bounties: {
        getBountyList: orpc.bounty.getBountyList,
        getActiveBountyList: orpc.bounty.getActiveBountyList,
        placeBounty: orpc.bounty.placeBounty,
        deleteBounty: orpc.bounty.deleteBounty,
    },

    // Suggestions Routes
    suggestions: {
        getSuggestions: orpc.suggestions.getSuggestions,
        getVoteHistory: orpc.suggestions.getVoteHistory,
        getComments: orpc.suggestions.getComments,
        create: orpc.suggestions.create,
        vote: orpc.suggestions.vote,
        comment: orpc.suggestions.comment,
        changeState: orpc.suggestions.changeState,
        getAvailablePolls: orpc.suggestions.getAvailablePolls,
        submitPollResponse: orpc.suggestions.submitPollResponse,
        getPollResults: orpc.suggestions.getPollResults,
    },

    missions: {
        getList: orpc.mission.getList,
        start: orpc.mission.start,
        cancel: orpc.mission.cancel,
        getCurrent: orpc.mission.getCurrent,
    },

    shrine: {
        getGoal: orpc.shrine.getGoal,
        getDonations: orpc.shrine.getDonations,
        donate: orpc.shrine.donate,
        getActiveBuff: orpc.shrine.getActiveBuff,
        isBuffActive: orpc.shrine.isBuffActive,
    },

    auctions: {
        getList: orpc.auction.getList,
        createListing: orpc.auction.createListing,
        buyoutListing: orpc.auction.buyoutListing,
        cancelListing: orpc.auction.cancelListing,
    },

    rooftop: {
        rooftopList: orpc.battle.rooftopList,
        beginRooftopBattle: orpc.battle.beginRooftopBattle,
    },

    social: {
        getFriends: orpc.social.getFriends,
        getFriendRequests: orpc.social.getFriendRequests,
        sendFriendRequest: orpc.social.sendFriendRequest,
        respondToFriendRequest: orpc.social.respondToFriendRequest,
        removeFriend: orpc.social.removeFriend,
        updateFriendNote: orpc.social.updateFriendNote,
        updateStatusMessage: orpc.social.updateStatusMessage,
        updatePrivacySettings: orpc.social.updatePrivacySettings,
        getRivals: orpc.social.getRivals,
        addRival: orpc.social.addRival,
        removeRival: orpc.social.removeRival,
        updateRivalNote: orpc.social.updateRivalNote,
    },

    story: {
        getSeasons: orpc.story.getSeasons,
        makeChoice: orpc.story.makeChoice,
        completeEpisode: orpc.story.completeEpisode,
    },

    scavenging: {
        generateGrid: orpc.skills.scavenging.generateGrid,
        revealCell: orpc.skills.scavenging.revealCell,
        getActiveSession: orpc.skills.scavenging.getActiveSession,
        devGrid: orpc.skills.scavenging.devGrid,
        endSession: orpc.skills.scavenging.endSession,
        resetGrid: orpc.skills.scavenging.resetGrid,
    },

    mining: {
        startMining: orpc.skills.startMining,
        processSwing: orpc.skills.processSwing,
        getMiningSession: orpc.skills.getMiningSession,
        cancelMining: orpc.skills.cancelMining,
    },

    // Explore Routes
    explore: {
        getMapByLocation: orpc.explore.getMapByLocation,
        interactWithNode: orpc.explore.interactWithNode,
        completeNode: orpc.explore.completeNode,
        processMiningOperation: orpc.explore.processMiningOperation,
        processForagingOperation: orpc.explore.processForagingOperation,
        makeScavengeChoice: orpc.explore.makeScavengeChoice,
        changeMapLocation: orpc.explore.changeMapLocation,
    },

    dev: {
        fullHeal: orpc.dev.fullHeal,
        fullHealAll: orpc.dev.fullHealAll,
        startPvpBattle: orpc.dev.startPvpBattle,
        resetBattles: orpc.dev.resetBattles,
        addRandomEffects: orpc.dev.addRandomEffects,
        removeAllEffects: orpc.dev.removeAllEffects,
        addXp: orpc.dev.addXp,
        addCash: orpc.dev.addCash,
        addStats: orpc.dev.addStats,
        removeStats: orpc.dev.removeStats,
        addAllItems: orpc.dev.addAllItems,
        completeQuests: orpc.dev.completeQuests,
        resetQuests: orpc.dev.resetQuests,
        addItem: orpc.dev.addItem,
        hatchEggs: orpc.dev.hatchEggs,
        setFullPetHappiness: orpc.dev.setFullPetHappiness,
        addPetXp: orpc.dev.addPetXp,
        deleteExploreNodes: orpc.dev.deleteExploreNodes,
        getPetsList: orpc.dev.getPetsList,
        randomRoguelike: orpc.dev.randomRoguelike,
        notify: orpc.dev.notify,
        testEmail: orpc.dev.testEmail,
        sendAiChat: orpc.dev.sendAiChat,
    },
};

export interface QueryOptions {
    staleTime?: number;
    enabled?: boolean;
    // [key: string]: unknown;
}
